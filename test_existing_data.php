<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Company;
use App\Models\CompanyHistory;

echo "Testing with Existing Data\n";
echo "==========================\n\n";

// Get a company to test with
$company = Company::find(3);
if (!$company) {
    echo "No company found with ID 3\n";
    exit;
}

echo "Testing with company: {$company->name} (ID: {$company->company_id})\n";

// First, set up some existing sectors
echo "Setting up existing sectors [1, 2]...\n";
$company->sectors()->sync([1, 2]);

// Get the current sectors to verify they're stored as integers
$currentSectors = $company->sectors()->pluck('sectors.sector_id')->toArray();
echo "Current sectors from DB: " . json_encode($currentSectors) . " (types: " . implode(', ', array_map('gettype', $currentSectors)) . ")\n";

// Check current history count
$initialHistoryCount = CompanyHistory::count();
echo "Initial history count: $initialHistoryCount\n\n";

// Test: Sync with same sectors but as strings (should NOT create history)
echo "Test: Syncing with same sectors as strings ['1', '2']...\n";
$stringSectorIds = ['1', '2'];
echo "Input sector IDs: " . json_encode($stringSectorIds) . " (types: " . implode(', ', array_map('gettype', $stringSectorIds)) . ")\n";

$company->syncSectors($stringSectorIds);

$afterSync = CompanyHistory::count();
echo "History count after sync: $afterSync\n";

if ($afterSync === $initialHistoryCount) {
    echo "✓ No history created when syncing identical data with different types (correct behavior)\n";
} else {
    echo "✗ History was created when syncing identical data with different types (incorrect behavior)\n";
    
    $latestHistory = CompanyHistory::where('change_type', 'sectors')->latest()->first();
    if ($latestHistory) {
        echo "Latest sectors history:\n";
        echo "- Old Sector IDs: " . json_encode($latestHistory->old_sector_ids) . "\n";
        echo "- New Sector IDs: " . json_encode($latestHistory->new_sector_ids) . "\n";
    }
}

echo "\nTest completed.\n";
