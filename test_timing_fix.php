<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Company;
use App\Models\CompanyHistory;

echo "Testing Timing Fix\n";
echo "==================\n\n";

// Get a company to test with
$company = Company::find(3);
if (!$company) {
    echo "No company found with ID 3\n";
    exit;
}

echo "Testing with company: {$company->name} (ID: {$company->company_id})\n";

// Set up some initial sectors for testing
echo "Setting up initial sectors [1, 2]...\n";
$company->sectors()->sync([1, 2]);

// Get current sectors to verify
$currentSectors = $company->sectors()->pluck('sectors.sector_id')->toArray();
echo "Current sectors: " . json_encode($currentSectors) . "\n\n";

// Check current history count
$initialHistoryCount = CompanyHistory::count();
echo "Initial history count: $initialHistoryCount\n\n";

echo "Now test by editing the company through the Filament interface.\n";
echo "The logs should show:\n";
echo "1. 'Old Country IDs captured: [...]' - showing the correct old data\n";
echo "2. 'New Country IDs: [...]' - showing the new data\n";
echo "3. 'Comparison result: DIFFERENT' or 'SAME' - showing if history should be created\n\n";

echo "Check the logs with: tail -f storage/logs/laravel.log\n";
echo "Then edit company ID {$company->company_id} in the admin panel.\n";

echo "\nTest setup completed.\n";
