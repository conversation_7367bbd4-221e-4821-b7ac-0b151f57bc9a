<?php

namespace App\Providers;

use App\Console\Commands\MakeFilamentResource;
use App\Console\Commands\MakeModel;
use App\Models\Company;
use App\Models\CompanyAddress;
use App\Models\CompanyCountry;
use App\Models\CompanyIndustry;
use App\Models\CompanySector;
use App\Models\Subscription;
use App\Models\SubscriptionLicense;
use App\Models\User;
use App\Observers\CompanyObserver;
use App\Observers\CompanyAddressObserver;
use App\Observers\CompanyCountryObserver;
use App\Observers\CompanyIndustryObserver;
use App\Observers\CompanySectorObserver;
use App\Observers\SubscriptionObserver;
use App\Observers\SubscriptionLicenseObserver;
use App\Observers\UserObserver;
use App\Services\ApiResponseService;
use App\Services\LogService;
use App\Services\MoneyFormatter;
use App\Services\OtpService;
use App\Services\RbacService;
use App\Services\SubscriptionProgressService;
use App\Support\Utils as SupportUtils;
use BezhanSalleh\FilamentShield\Support\Utils;
use Illuminate\Support\Facades\Vite;
use Illuminate\Support\ServiceProvider;
use TomatoPHP\FilamentSubscriptions\Facades\FilamentSubscriptions;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                MakeFilamentResource::class,
                MakeModel::class
            ]);
        }

        $this->app->singleton(ApiResponseService::class, function ($app) {
            return new ApiResponseService();
        });

        $this->app->singleton(LogService::class, function ($app) {
            return new LogService();
        });

        $this->app->singleton(OtpService::class, function ($app) {
            return new OtpService();
        });

        $this->app->singleton(RbacService::class, function ($app) {
            return new RbacService();
        });

        $this->app->singleton(MoneyFormatter::class, function ($app) {
            return new MoneyFormatter();
        });

        $this->app->singleton(SubscriptionProgressService::class, function ($app) {
            return new SubscriptionProgressService();
        });

        $this->app->bind(Utils::class, SupportUtils::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Vite::prefetch(concurrency: 3);

        FilamentSubscriptions::register(
            [
                \TomatoPHP\FilamentSubscriptions\Services\Contracts\Subscriber::make('company')
                    ->model(\App\Models\Company::class),
            ]
        );

        // Register company-related observers
        Company::observe(CompanyObserver::class);
        CompanyAddress::observe(CompanyAddressObserver::class);
        CompanyCountry::observe(CompanyCountryObserver::class);
        CompanyIndustry::observe(CompanyIndustryObserver::class);
        CompanySector::observe(CompanySectorObserver::class);
        User::observe(UserObserver::class);

        // Register subscription observers
        Subscription::observe(SubscriptionObserver::class);
        SubscriptionLicense::observe(SubscriptionLicenseObserver::class);
    }
}
