<?php

namespace App\Models;

use App\Scopes\SortingScope;
use App\Traits\Blamable;
use Illuminate\Database\Eloquent\Relations\Pivot;

class CompanyIndustry extends Pivot
{
    use Blamable;

    protected $table = 'company_industries';
    public $incrementing = true;

    /**
     * The primary key column name.
     *
     * @var string
     */
    protected $primaryKey = 'company_industry_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
    */
    protected $fillable = [
        'company_id',
        'industry_id',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by',
        'deleted_at',
        'deleted_by'
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
            //'password' => 'hashed',
            //'countries' => 'array',
        ];
    }

    /**
     * Attributes to include in the Audit.
     *
     * @var array
     */
    // protected $auditInclude = [
    //     'title',
    //     'content',
    // ];

    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());

        static::creating(function ($model) {
            self::setCreatedUpdatedBy($model);
        });

        static::saving(function ($model) {
            self::setUpdatedBy($model);
        });

        static::deleting(function ($model) {
            self::setDeletedBy($model);
        });

        return parent::booted();
    }

    /**
     * Get the company that owns the pivot.
     */
    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'company_id');
    }

    /**
     * Get the industry that owns the pivot.
     */
    public function industry()
    {
        return $this->belongsTo(Industry::class, 'industry_id', 'industry_id');
    }

}