<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CompanyHistory extends Model
{
    /**
     * The primary key column name.
     *
     * @var string
     */
    protected $primaryKey = 'company_history_id';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'company_history';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'company_id',
        'action',
        'related_model',
        'related_model_id',
        'related_action',
        'performed_by',
        'public_company_id',
        'name',
        'status_id',
        'timezone',
        'website',
        'number_of_employees',
        'registration_type',
        'invitation_expires_at',
        'last_activity_at',
        'original_created_at',
        'original_updated_at',
        'original_deleted_at',
        'original_created_by',
        'original_updated_by',
        'original_deleted_by',
        'changed_fields',
        'old_values',
        'new_values',
        'notes',
        'request_data',
        'ip_address',
        'user_agent',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'changed_fields' => 'array',
            'old_values' => 'array',
            'new_values' => 'array',
            'request_data' => 'array',
            'invitation_expires_at' => 'datetime',
            'last_activity_at' => 'datetime',
            'original_created_at' => 'datetime',
            'original_updated_at' => 'datetime',
            'original_deleted_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Get the company that this history record belongs to.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'company_id');
    }

    /**
     * Get the user who performed this action.
     */
    public function performedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'performed_by', 'user_id');
    }

    /**
     * Get the status at the time of this history record.
     */
    public function status(): BelongsTo
    {
        return $this->belongsTo(Status::class, 'status_id', 'status_id');
    }

    /**
     * Get the user who originally created the company.
     */
    public function originalCreatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'original_created_by', 'user_id');
    }

    /**
     * Get the user who originally updated the company.
     */
    public function originalUpdatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'original_updated_by', 'user_id');
    }

    /**
     * Get the user who originally deleted the company.
     */
    public function originalDeletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'original_deleted_by', 'user_id');
    }

    /**
     * Scope to filter by action type.
     */
    public function scopeByAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope to filter by related model.
     */
    public function scopeByRelatedModel($query, string $model)
    {
        return $query->where('related_model', $model);
    }

    /**
     * Scope to get recent history.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Get a human-readable description of the action.
     */
    public function getActionDescriptionAttribute(): string
    {
        $description = ucfirst($this->action);
        
        if ($this->related_model) {
            $modelName = class_basename($this->related_model);
            $description .= " (related {$modelName} {$this->related_action})";
        }
        
        return $description;
    }

    /**
     * Check if this history record has field changes.
     */
    public function hasFieldChanges(): bool
    {
        return !empty($this->changed_fields) && (
            !empty($this->old_values) || !empty($this->new_values)
        );
    }

    /**
     * Get the changes in a formatted way.
     */
    public function getFormattedChanges(): array
    {
        if (!$this->hasFieldChanges()) {
            return [];
        }

        $changes = [];
        $changedFields = $this->changed_fields ?? [];
        $oldValues = $this->old_values ?? [];
        $newValues = $this->new_values ?? [];

        foreach ($changedFields as $field) {
            $changes[$field] = [
                'old' => $oldValues[$field] ?? null,
                'new' => $newValues[$field] ?? null,
            ];
        }

        return $changes;
    }
}
