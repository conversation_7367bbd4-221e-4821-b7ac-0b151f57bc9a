<?php

namespace App\Models;

use App\Enums\DefaultValues;
use App\Enums\Subscriptions;
use App\Helpers\AppHelper;
use App\Helpers\BillingsHelper;
use App\Helpers\InvitationHelper;
use App\Helpers\RbacHelper;
use App\Helpers\StatusHelper;
use App\Traits\Blamable;
use App\Traits\Timezones;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;
use App\Scopes\SortingScope;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Laravelcm\Subscriptions\Traits\HasPlanSubscriptions;

class Company extends Model
{
    use Timezones, HasFactory, Notifiable, SoftDeletes, Blamable, HasPlanSubscriptions;

    /**
     * The primary key column name.
     *
     * @var string
     */
    protected $primaryKey = 'company_id';



    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'public_company_id',
        'status_id',
        'name',
        'timezone',
        'website',
        'number_of_employees',
        'created_by',
        'updated_at',
        'deleted_at',
        'created_by',
        'updated_by',
        'deleted_by',
        'registration_type',
        'invitation_expires_at'
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'updated_at' => 'datetime',
            'invitation_expires_at' => 'datetime',
        ];
    }

    public function primaryContact(): HasOne
    {
        return $this->hasOne(User::class, 'company_id')
            ->where('is_primary_customer', true)
            ->where('type', 'customer')
            ->withTrashed();
    }

    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());

        static::creating(function ($model) {
            self::setCreatedUpdatedBy($model);
        });

        static::created(function ($model) {
            $model->public_company_id = AppHelper::getFormattedId($model->company_id, DefaultValues::CUSTOMER_PREFIX->get());

            // Create primary contact user if data is provided
            if (request()->has('primary_contact')) {
                $pendingStatusId = StatusHelper::getStatusByAttributeName('name', 'pending', StatusHelper::STATUS_TYPE_USER);

                $userData = request()->get('primary_contact');

                // Set invitation expiry date for the company
                // $model->invitation_expires_at = now()->addDays(7);

                $user = User::create([
                    'company_id' => $model->company_id,
                    'status_id' => $pendingStatusId,
                    'name' => $userData['name'],
                    'email' => $userData['email'],
                    'phone_number' => $userData['phone_number'],
                    'timezone' => $userData['timezone'],
                    'type' => 'customer',
                    'is_primary_customer' => true,
                    'created_by' => self::getBlamableId(),
                ]);

                $primaryRole = Role::where('name', RbacHelper::ROLE_PRIMARY)->where('guard_name', 'api')->first();
                if (!$primaryRole) {
                    $primaryRole = Role::create(['type' => RbacHelper::ROLE_PRIMARY, 'name' => RbacHelper::ROLE_PRIMARY, 'guard_name' => 'api']);
                }
                $user->assignRole($primaryRole);

                // Send invitation email automatically when creating a new company
                InvitationHelper::sendUserInvitation($user->email, $user->name);
            }

            $model->saveQuietly();
        });

        static::saving(function ($model) {
            self::setUpdatedBy($model);
        });

        static::deleting(function ($model) {
            self::setDeletedBy($model);

            // Softly delete all users associated with this company
            if (!$model->isForceDeleting()) {
                // Soft delete all linked customer users
                User::where('company_id', $model->company_id)
                    ->where('type', 'customer')
                    ->get()
                    ->each(function ($user) {
                        $user->delete();
                    });
            }
        });

        static::restored(function ($model) {
            // Restore all soft-deleted users associated with this company
            User::withTrashed()
                ->where('company_id', $model->company_id)
                ->where('type', 'customer')
                ->get()
                ->each(function ($user) {
                    $user->restore();
                });
        });

        return parent::booted();
    }

    public function users()
    {
        return $this->hasMany(User::class, 'company_id', 'company_id');
    }

    /**
     * Get the status associated with the company.
     */
    public function status()
    {
        return $this->belongsTo(Status::class, 'status_id', 'status_id');
    }

    /**
     * Get the countries associated with the company.
     */
    public function countries()
    {
        return $this->belongsToMany(Country::class, 'company_countries', 'company_id', 'country_id')
            ->withTimestamps();
    }

    /**
     * Get the sectors associated with the company.
     */
    public function sectors()
    {
        return $this->belongsToMany(Sector::class, 'company_sectors', 'company_id', 'sector_id')
            ->withTimestamps();
    }

    /**
     * Get the industries associated with the company.
     */
    public function industries()
    {
        return $this->belongsToMany(Industry::class, 'company_industries', 'company_id', 'industry_id')
            ->withTimestamps();
    }

    /**
     * Get company addresses.
     */
    public function addresses()
    {
        return $this->hasMany(CompanyAddress::class, 'company_id', 'company_id');
    }

    public function companyAddress(int $countryId)
    {
        return $this->addresses()->where([
            ['address_type', BillingsHelper::ADDRESS_TYPE_COMPANY],
            ['country_id', $countryId]
        ])->first();
    }

    public function billingAddress(int $countryId)
    {
        return $this->addresses()->where([
            ['address_type', BillingsHelper::ADDRESS_TYPE_BILLING],
            ['country_id', $countryId]
        ])->first();
    }

    public function companyLicenses()
    {
        return $this->hasMany(SubscriptionLicense::class, 'company_id', 'company_id');
    }

    /**
     * Get the history records for this company.
     */
    public function history()
    {
        return $this->hasMany(CompanyHistory::class, 'company_id', 'company_id')
            ->orderBy('created_at', 'desc');
    }



    /**
     * Check if the company has a previous environment (excluding the first license).
     * Returns true if there is more than one license with environment_status set.
     */
    public function hasPreviousEnvironment()
    {
        $companyLicenses = $this->companyLicenses()
            ->whereNotNull('environment_status')
            ->orderBy('created_at', 'asc');

        $licenses = $companyLicenses->get();

        // If there is more than one environment license, return true (previous exists)
        return $licenses->count() > 1;
    }

    

    protected $cast = [
        'updated_at' => 'datetime'
    ];

    public function getName(): string
    {
        return $this->name ?? '(Not Set)';
    }

    /**
     * Determine if the company profile is complete.
     */
    public function isProfileCompleted(): bool
    {
        return !empty($this->name) && !empty($this->countries) && !empty($this->sectors) && !empty($this->industries);
    }



    /**
     * Check if the company has a subscription
     */
    public function hasSubscription(): bool
    {
        return $this->planSubscriptions()->exists();
    }

    /**
     * Check if the company has users other than the primary user
     */
    public function hasNonPrimaryUsers(): bool
    {
        return $this->users()
            ->where('type', 'customer')
            ->where('is_primary_customer', false)
            ->exists();
    }

    /**
     * check if customer has previous trial subscriptions
     */
    public function hasPreviousSubscriptionsOfType(Subscriptions $subscriptionType = Subscriptions::TRIAL_VERSION): bool
    {
        $customerSubscriptions = $this->planSubscriptions;

        if (!empty($customerSubscriptions)) {
            $planIds = collect($customerSubscriptions)->pluck('plan_id')->unique()->toArray();

            return Plan::whereIn('id', $planIds)
                ->where('slug', $subscriptionType)
                ->exists();
        }

        return false;
    }

    /**
     * Check if the customer has a non-active subscription with the given feature ID.
     */
    public function hasNonActiveSubscriptionWithFeature(int $feature_id): bool
    {
        return $this->planSubscriptions()
            ->where('subscription_status', '!=', Subscriptions::SUBSCRIPTION_STATUS_ACTIVE->value)
            ->whereHas('subscriptionFeatures', function ($q) use ($feature_id) {
                $q->where('feature_id', $feature_id);
            })
            ->with('subscriptionFeatures')
            ->exists();
    }
}