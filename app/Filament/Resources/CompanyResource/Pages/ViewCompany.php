<?php

namespace App\Filament\Resources\CompanyResource\Pages;

use App\Filament\Resources\CompanyResource;
use App\Helpers\InvitationHelper;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\View\View;

class ViewCompany extends ViewRecord
{
    protected static string $resource = CompanyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('viewHistory')
                ->label('Company History')
                ->icon('heroicon-o-clock')
                ->color('info')
                ->tooltip('View company history')
                ->visible(fn() => auth()->user()->can('view_company'))
                ->modalHeading('Company History')
                ->modalWidth(\Filament\Support\Enums\MaxWidth::SevenExtraLarge)
                ->modalContent(function (): View {
                    return view('filament.resources.company-resource.company-history', [
                        'company' => $this->record,
                    ]);
                })
                ->modalFooterActions([]),
            Actions\Action::make('resendInvitation')
                ->label('Resend Invitation')
                ->icon('heroicon-o-envelope')
                ->color('warning')
                ->requiresConfirmation()
                ->modalHeading('Resend Invitation')
                ->modalDescription('Are you sure you want to resend the invitation email to the primary contact?')
                ->modalSubmitActionLabel('Yes, Resend')
                ->action(function () {
                    $company = $this->record;
                    if ($primaryContact = $company->primaryContact) {
                        InvitationHelper::sendUserInvitation($primaryContact->email, $primaryContact->name);
                        Notification::make()
                            ->title('Invitation email has been sent to ' . $primaryContact->email)
                            ->success()
                            ->send();
                    } else {
                        Notification::make()
                            ->title('No primary contact found for this company')
                            ->danger()
                            ->send();
                    }
                })
                ->visible(fn () =>
                    $this->record->primaryContact !== null &&
                    !$this->record->primaryContact->accepted &&
                    auth()->user()->can('update_customer::user')
//                    $this->record->primaryContact->status_id !== \App\Helpers\StatusHelper::getStatusByAttributeName('name', 'active', \App\Helpers\StatusHelper::STATUS_TYPE_USER)
                ),
            Actions\EditAction::make(),
            Actions\DeleteAction::make()
                ->requiresConfirmation()
                ->modalDescription(CompanyResource::getModelDeletedNotificationMessage()),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $company = $this->record;
        if ($primaryContact = $company->primaryContact) {
            $data['primary_contact'] = [
                'name' => $primaryContact->name . ($primaryContact->deleted_at ? ' (Deleted)' : ''),
                'email' => $primaryContact->email,
                'phone_number' => $primaryContact->phone_number,
                'timezone' => $primaryContact->timezone,
            ];
        }

        return $data;
    }

}
