<?php

namespace App\Filament\Resources\CompanyResource\Pages;

use App\Filament\Resources\CompanyResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Log;

class EditCompany extends EditRecord
{
    protected static string $resource = CompanyResource::class;

    // Store old relationship data before any form processing
    protected array $oldCountryIds = [];
    protected array $oldSectorIds = [];
    protected array $oldIndustryIds = [];

    public function mount(int | string $record): void
    {
        parent::mount($record);

        // Capture old relationship data when the page is first loaded
        $this->oldCountryIds = $this->record->countries()->pluck('countries.id')->toArray();
        $this->oldSectorIds = $this->record->sectors()->pluck('sectors.sector_id')->toArray();
        $this->oldIndustryIds = $this->record->industries()->pluck('industries.industry_id')->toArray();

        // Convert to integers for consistent comparison
        $this->oldCountryIds = array_map('intval', $this->oldCountryIds);
        $this->oldSectorIds = array_map('intval', $this->oldSectorIds);
        $this->oldIndustryIds = array_map('intval', $this->oldIndustryIds);

        // Sort for consistent comparison
        sort($this->oldCountryIds);
        sort($this->oldSectorIds);
        sort($this->oldIndustryIds);

        Log::info('Old data captured on mount - Countries: ' . json_encode($this->oldCountryIds));
        Log::info('Old data captured on mount - Sectors: ' . json_encode($this->oldSectorIds));
        Log::info('Old data captured on mount - Industries: ' . json_encode($this->oldIndustryIds));
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $company = $this->record;
        if ($primaryContact = $company->primaryContact) {
            $data['primary_contact'] = [
                'name' => $primaryContact->name,
                'email' => $primaryContact->email,
                'phone_number' => $primaryContact->phone_number,
                'timezone' => $primaryContact->timezone,
            ];
        }

        // Load relationship data for the form
        $data['countries'] = $this->record->countries()->pluck('countries.id')->toArray();
        $data['sectors'] = $this->record->sectors()->pluck('sectors.sector_id')->toArray();
        $data['industries'] = $this->record->industries()->pluck('industries.industry_id')->toArray();

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if (empty($data['password'])) {
            unset($data['password']);
        }

        // Update primary contact if it exists
        if (isset($data['primary_contact']) && $this->record->primaryContact) {
            $this->record->primaryContact->update($data['primary_contact']);
        }

        unset($data['primary_contact']);

        return $data;
    }

    protected function afterSave(): void
    {
        $countries = $this->data['countries'] ?? [];
        $sectors = $this->data['sectors'] ?? [];
        $industries = $this->data['industries'] ?? [];

        // Convert new data to integers and sort for comparison
        $newCountryIds = array_map('intval', $countries);
        $newSectorIds = array_map('intval', $sectors);
        $newIndustryIds = array_map('intval', $industries);

        sort($newCountryIds);
        sort($newSectorIds);
        sort($newIndustryIds);

        Log::info('Old Country IDs: ' . json_encode($this->oldCountryIds));
        Log::info('New Country IDs: ' . json_encode($newCountryIds));
        Log::info('Comparison result: ' . ($this->oldCountryIds !== $newCountryIds ? 'DIFFERENT' : 'SAME'));

        // Manually sync the relationships
        $this->record->countries()->sync($countries);
        $this->record->sectors()->sync($sectors);
        $this->record->industries()->sync($industries);

        // Create history records for changed relationships
        if ($this->oldCountryIds !== $newCountryIds) {
            \App\Observers\CompanyObserver::createPivotHistory($this->record, 'countries', $this->oldCountryIds, $newCountryIds);
        }

        if ($this->oldSectorIds !== $newSectorIds) {
            \App\Observers\CompanyObserver::createPivotHistory($this->record, 'sectors', $this->oldSectorIds, $newSectorIds);
        }

        if ($this->oldIndustryIds !== $newIndustryIds) {
            \App\Observers\CompanyObserver::createPivotHistory($this->record, 'industries', $this->oldIndustryIds, $newIndustryIds);
        }

        // Redirect to the view page which will show fresh data including audit logs
        $this->redirect(static::getResource()::getUrl('view', ['record' => $this->record]));
    }
}
