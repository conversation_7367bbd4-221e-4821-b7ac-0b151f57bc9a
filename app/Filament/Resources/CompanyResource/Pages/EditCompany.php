<?php

namespace App\Filament\Resources\CompanyResource\Pages;

use App\Filament\Resources\CompanyResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCompany extends EditRecord
{
    protected static string $resource = CompanyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $company = $this->record;
        if ($primaryContact = $company->primaryContact) {
            $data['primary_contact'] = [
                'name' => $primaryContact->name,
                'email' => $primaryContact->email,
                'phone_number' => $primaryContact->phone_number,
                'timezone' => $primaryContact->timezone,
            ];
        }

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if (empty($data['password'])) {
            unset($data['password']); 
        }

        // Update primary contact if it exists
        if (isset($data['primary_contact']) && $this->record->primaryContact) {
            $this->record->primaryContact->update($data['primary_contact']);
        }

        unset($data['primary_contact']);

        $countries = $this->data['countries'] ?? [];
        $sectors = $this->data['sectors'] ?? [];
        $industries = $this->data['industries'] ?? [];

        // Sync relationships with history tracking
        $this->record->syncCountries($countries);
        $this->record->syncSectors($sectors);
        $this->record->syncIndustries($industries);

        return $data;
    }

    protected function afterSave(): void
    {
        // Redirect to the view page which will show fresh data including audit logs
        $this->redirect(static::getResource()::getUrl('view', ['record' => $this->record]));
    }
}
