<?php

namespace App\Observers;

use App\Models\CompanyCountry;
use App\Models\Company;
use App\Observers\CompanyObserver;

class CompanyCountryObserver
{
    /**
     * Handle the CompanyCountry "created" event.
     */
    public function created(CompanyCountry $companyCountry): void
    {
        $this->logCompanyRelatedChange($companyCountry, 'created');
    }

    /**
     * Handle the CompanyCountry "deleted" event.
     */
    public function deleted(CompanyCountry $companyCountry): void
    {
        $this->logCompanyRelatedChange($companyCountry, 'deleted');
    }

    /**
     * Log the company country change as a company-related history entry.
     */
    private function logCompanyRelatedChange(CompanyCountry $companyCountry, string $action): void
    {
        $company = Company::find($companyCountry->company_id);
        
        if (!$company) {
            return;
        }

        $data = [
            'company_country_id' => $companyCountry->company_country_id,
            'country_id' => $companyCountry->country_id,
            'country_name' => $companyCountry->country->name ?? null,
        ];

        CompanyObserver::createRelatedModelHistory(
            $company,
            CompanyCountry::class,
            $companyCountry->company_country_id,
            $action,
            $data
        );
    }
}
