<?php

namespace App\Observers;

use App\Models\CompanyIndustry;
use App\Models\Company;
use App\Observers\CompanyObserver;

class CompanyIndustryObserver
{
    /**
     * Handle the CompanyIndustry "created" event.
     */
    public function created(CompanyIndustry $companyIndustry): void
    {
        $this->logCompanyRelatedChange($companyIndustry, 'created');
    }

    /**
     * Handle the CompanyIndustry "deleted" event.
     */
    public function deleted(CompanyIndustry $companyIndustry): void
    {
        $this->logCompanyRelatedChange($companyIndustry, 'deleted');
    }

    /**
     * Log company-related changes.
     */
    private function logCompanyRelatedChange(CompanyIndustry $companyIndustry, string $action): void
    {
        $company = Company::find($companyIndustry->company_id);
        if (!$company) {
            return;
        }

        $data = [
            'company_industry_id' => $companyIndustry->company_industry_id,
            'industry_id' => $companyIndustry->industry_id,
            'industry_name' => $companyIndustry->industry?->name ?? 'Unknown',
        ];

        CompanyObserver::createRelatedModelHistory(
            $company,
            CompanyIndustry::class,
            $companyIndustry->company_industry_id,
            $action,
            $data
        );
    }
}
