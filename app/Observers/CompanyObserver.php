<?php

namespace App\Observers;

use App\Models\Company;
use App\Models\CompanyHistory;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

class CompanyObserver
{
    /**
     * Handle the Company "created" event.
     */
    public function created(Company $company): void
    {
        $this->createHistoryRecord($company, 'created');
    }

    /**
     * Handle the Company "updated" event.
     */
    public function updated(Company $company): void
    {
        $this->createHistoryRecord($company, 'updated', $company->getChanges(), $company->getOriginal());
    }

    /**
     * Handle the Company "deleted" event.
     */
    public function deleted(Company $company): void
    {
        $this->createHistoryRecord($company, 'deleted');
    }

    /**
     * Handle the Company "restored" event.
     */
    public function restored(Company $company): void
    {
        $this->createHistoryRecord($company, 'restored');
    }

    /**
     * Create a history record for the company action.
     */
    private function createHistoryRecord(
        Company $company, 
        string $action, 
        array $changes = [], 
        array $original = []
    ): void {
        $performedBy = $this->getPerformedByUserId();
        
        // Determine what fields actually changed
        $changedFields = [];
        $oldValues = [];
        $newValues = [];
        
        if (!empty($changes) && $action === 'updated') {
            // Filter out timestamps and system fields from changes
            $excludeFields = ['updated_at', 'last_activity_at'];
            
            foreach ($changes as $field => $newValue) {
                if (!in_array($field, $excludeFields)) {
                    $changedFields[] = $field;
                    $oldValues[$field] = $original[$field] ?? null;
                    $newValues[$field] = $newValue;
                }
            }
        }

        CompanyHistory::create([
            'company_id' => $company->company_id,
            'action' => $action,
            'performed_by' => $performedBy,
            
            // Company data snapshot
            'public_company_id' => $company->public_company_id,
            'name' => $company->name,
            'status_id' => $company->status_id,
            'timezone' => $company->timezone,
            'website' => $company->website,
            'number_of_employees' => $company->number_of_employees,
            'registration_type' => $company->registration_type,
            'invitation_expires_at' => $company->invitation_expires_at,
            'last_activity_at' => $company->last_activity_at,
            
            // Original timestamps
            'original_created_at' => $company->created_at,
            'original_updated_at' => $company->updated_at,
            'original_deleted_at' => $company->deleted_at,
            'original_created_by' => $company->created_by,
            'original_updated_by' => $company->updated_by,
            'original_deleted_by' => $company->deleted_by,
            
            // Change tracking
            'changed_fields' => !empty($changedFields) ? $changedFields : null,
            'old_values' => !empty($oldValues) ? $oldValues : null,
            'new_values' => !empty($newValues) ? $newValues : null,
            
            // Request context
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'request_data' => $this->getRequestContext(),
        ]);
    }

    /**
     * Create a history record for related model changes.
     */
    public static function createRelatedModelHistory(
        Company $company,
        string $relatedModel,
        int $relatedModelId,
        string $relatedAction,
        array $data = []
    ): void {
        $performedBy = (new self())->getPerformedByUserId();

        CompanyHistory::create([
            'company_id' => $company->company_id,
            'action' => 'updated',
            'related_model' => $relatedModel,
            'related_model_id' => $relatedModelId,
            'related_action' => $relatedAction,
            'performed_by' => $performedBy,
            
            // Company data snapshot
            'public_company_id' => $company->public_company_id,
            'name' => $company->name,
            'status_id' => $company->status_id,
            'timezone' => $company->timezone,
            'website' => $company->website,
            'number_of_employees' => $company->number_of_employees,
            'registration_type' => $company->registration_type,
            'invitation_expires_at' => $company->invitation_expires_at,
            'last_activity_at' => $company->last_activity_at,
            
            // Original timestamps
            'original_created_at' => $company->created_at,
            'original_updated_at' => $company->updated_at,
            'original_deleted_at' => $company->deleted_at,
            'original_created_by' => $company->created_by,
            'original_updated_by' => $company->updated_by,
            'original_deleted_by' => $company->deleted_by,
            
            // Related model data
            'new_values' => $data,
            
            // Request context
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'request_data' => (new self())->getRequestContext(),
        ]);
    }

    /**
     * Get the ID of the user performing the action.
     */
    private function getPerformedByUserId(): ?int
    {
        // Try to get from authenticated user
        if (Auth::check()) {
            return Auth::id();
        }

        // Try to get from request (for API calls)
        if (Request::has('performed_by')) {
            return Request::get('performed_by');
        }

        // Try to get from company's created_by/updated_by fields
        return null;
    }

    /**
     * Get relevant request context for logging.
     */
    private function getRequestContext(): ?array
    {
        if (app()->runningInConsole()) {
            return ['source' => 'console'];
        }

        $context = [];
        
        // Add route information if available
        if (Request::route()) {
            $context['route'] = Request::route()->getName();
            $context['method'] = Request::method();
        }

        // Add relevant request parameters (exclude sensitive data)
        $excludeKeys = ['password', 'password_confirmation', 'token', 'api_key'];
        $requestData = Request::except($excludeKeys);
        
        if (!empty($requestData)) {
            $context['request_params'] = $requestData;
        }

        return !empty($context) ? $context : null;
    }
}
