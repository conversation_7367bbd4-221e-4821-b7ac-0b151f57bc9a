<?php

namespace App\Observers;

use App\Models\User;
use App\Models\Company;
use App\Observers\CompanyObserver;

class UserObserver
{
    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        if ($user->company_id && $user->type === 'customer') {
            $this->logCompanyRelatedChange($user, 'created');
        }
    }

    /**
     * Handle the User "updated" event.
     */
    public function updated(User $user): void
    {
        if ($user->company_id && $user->type === 'customer') {
            $this->logCompanyRelatedChange($user, 'updated', $user->getChanges());
        }
    }

    /**
     * Handle the User "deleted" event.
     */
    public function deleted(User $user): void
    {
        if ($user->company_id && $user->type === 'customer') {
            $this->logCompanyRelatedChange($user, 'deleted');
        }
    }

    /**
     * Handle the User "restored" event.
     */
    public function restored(User $user): void
    {
        if ($user->company_id && $user->type === 'customer') {
            $this->logCompanyRelatedChange($user, 'restored');
        }
    }

    /**
     * Log company-related changes.
     */
    private function logCompanyRelatedChange(User $user, string $action, array $changes = []): void
    {
        $company = Company::find($user->company_id);
        if (!$company) {
            return;
        }

        $data = [
            'user_id' => $user->user_id,
            'name' => $user->name,
            'email' => $user->email,
            'type' => $user->type,
            'is_primary_customer' => $user->is_primary_customer,
            'status_id' => $user->status_id,
        ];

        if (!empty($changes)) {
            $data['changes'] = $changes;
        }

        CompanyObserver::createRelatedModelHistory(
            $company,
            User::class,
            $user->user_id,
            $action,
            $data
        );
    }
}
