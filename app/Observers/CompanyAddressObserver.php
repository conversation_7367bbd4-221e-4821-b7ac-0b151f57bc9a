<?php

namespace App\Observers;

use App\Models\CompanyAddress;
use App\Models\Company;
use App\Observers\CompanyObserver;

class CompanyAddressObserver
{
    /**
     * Handle the CompanyAddress "created" event.
     */
    public function created(CompanyAddress $companyAddress): void
    {
        $this->logCompanyRelatedChange($companyAddress, 'created');
    }

    /**
     * Handle the CompanyAddress "updated" event.
     */
    public function updated(CompanyAddress $companyAddress): void
    {
        $this->logCompanyRelatedChange($companyAddress, 'updated', $companyAddress->getChanges());
    }

    /**
     * Handle the CompanyAddress "deleted" event.
     */
    public function deleted(CompanyAddress $companyAddress): void
    {
        $this->logCompanyRelatedChange($companyAddress, 'deleted');
    }

    /**
     * Log the company address change as a company-related history entry.
     */
    private function logCompanyRelatedChange(CompanyAddress $companyAddress, string $action, array $changes = []): void
    {
        $company = Company::find($companyAddress->company_id);
        
        if (!$company) {
            return;
        }

        $data = [
            'company_address_id' => $companyAddress->company_address_id,
            'country_id' => $companyAddress->country_id,
            'platform_bank_id' => $companyAddress->platform_bank_id,
            'tax_id' => $companyAddress->tax_id,
            'commercial_registration_number' => $companyAddress->commercial_registration_number,
            'state' => $companyAddress->state,
            'city' => $companyAddress->city,
            'zip' => $companyAddress->zip,
            'address_1' => $companyAddress->address_1,
            'currency_code' => $companyAddress->currency_code,
            'address_type' => $companyAddress->address_type,
        ];

        if (!empty($changes)) {
            $data['changes'] = $changes;
        }

        CompanyObserver::createRelatedModelHistory(
            $company,
            CompanyAddress::class,
            $companyAddress->company_address_id,
            $action,
            $data
        );
    }
}
