<?php

namespace App\Observers;

use App\Models\CompanySector;
use App\Models\Company;
use App\Observers\CompanyObserver;

class CompanySectorObserver
{
    /**
     * Handle the CompanySector "created" event.
     */
    public function created(CompanySector $companySector): void
    {
        $this->logCompanyRelatedChange($companySector, 'created');
    }

    /**
     * Handle the CompanySector "deleted" event.
     */
    public function deleted(CompanySector $companySector): void
    {
        $this->logCompanyRelatedChange($companySector, 'deleted');
    }

    /**
     * Log company-related changes.
     */
    private function logCompanyRelatedChange(CompanySector $companySector, string $action): void
    {
        $company = Company::find($companySector->company_id);
        if (!$company) {
            return;
        }

        $data = [
            'company_sector_id' => $companySector->company_sector_id,
            'sector_id' => $companySector->sector_id,
            'sector_name' => $companySector->sector?->name ?? 'Unknown',
        ];

        CompanyObserver::createRelatedModelHistory(
            $company,
            CompanySector::class,
            $companySector->company_sector_id,
            $action,
            $data
        );
    }
}
