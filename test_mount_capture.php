<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Company;
use App\Models\CompanyHistory;

echo "Testing Mount Data Capture\n";
echo "==========================\n\n";

// Get a company to test with
$company = Company::find(3);
if (!$company) {
    echo "No company found with ID 3\n";
    exit;
}

echo "Testing with company: {$company->name} (ID: {$company->company_id})\n";

// Get current relationships to verify
$currentCountries = $company->countries()->pluck('countries.id')->toArray();
$currentSectors = $company->sectors()->pluck('sectors.sector_id')->toArray();
$currentIndustries = $company->industries()->pluck('industries.industry_id')->toArray();

echo "Current countries: " . json_encode($currentCountries) . "\n";
echo "Current sectors: " . json_encode($currentSectors) . "\n";
echo "Current industries: " . json_encode($currentIndustries) . "\n\n";

// Check current history count
$initialHistoryCount = CompanyHistory::count();
echo "Initial history count: $initialHistoryCount\n\n";

echo "Now test by:\n";
echo "1. Navigate to edit company ID {$company->company_id} in the admin panel\n";
echo "2. Check the logs for 'Old data captured on mount' messages\n";
echo "3. Make changes to countries/sectors/industries\n";
echo "4. Save the form\n";
echo "5. Check the logs for comparison results\n\n";

echo "Expected log sequence:\n";
echo "1. 'Old data captured on mount - Countries: [...]' (should show current data)\n";
echo "2. 'New Country IDs: [...]' (should show the new data from form)\n";
echo "3. 'Comparison result: DIFFERENT' (if you made changes)\n\n";

echo "Check the logs with: tail -f storage/logs/laravel.log\n";

echo "\nTest setup completed.\n";
