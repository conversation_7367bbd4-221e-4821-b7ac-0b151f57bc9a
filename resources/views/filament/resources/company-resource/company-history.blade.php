<div class="space-y-6">
    @if($company->history->count() > 0)
        <div class="overflow-hidden bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 rounded-xl">
            <div class="overflow-x-auto">
                <table class="w-full text-sm">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th class="px-4 py-3 text-left font-medium text-gray-900 dark:text-gray-100">Date & Time</th>
                            <th class="px-4 py-3 text-left font-medium text-gray-900 dark:text-gray-100">Action</th>
                            <th class="px-4 py-3 text-left font-medium text-gray-900 dark:text-gray-100">Performed By</th>
                            <th class="px-4 py-3 text-left font-medium text-gray-900 dark:text-gray-100">Changes</th>
                            <th class="px-4 py-3 text-left font-medium text-gray-900 dark:text-gray-100">Related Model</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach($company->history as $historyRecord)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800/50">
                                <td class="px-4 py-3 text-gray-900 dark:text-gray-100">
                                    <div class="font-medium">
                                        {{ $historyRecord->created_at->format('M d, Y') }}
                                    </div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ $historyRecord->created_at->format('H:i:s') }}
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full
                                        @if($historyRecord->action === 'created') bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400
                                        @elseif($historyRecord->action === 'updated') bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400
                                        @elseif($historyRecord->action === 'deleted') bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400
                                        @elseif($historyRecord->action === 'restored') bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400
                                        @else bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400
                                        @endif">
                                        {{ ucfirst($historyRecord->action) }}
                                    </span>
                                    @if($historyRecord->related_action)
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            {{ ucfirst($historyRecord->related_action) }}
                                        </div>
                                    @endif
                                </td>
                                <td class="px-4 py-3 text-gray-900 dark:text-gray-100">
                                    @if($historyRecord->performedBy)
                                        <div class="font-medium">{{ $historyRecord->performedBy->name }}</div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">{{ $historyRecord->performedBy->email }}</div>
                                    @else
                                        <span class="text-gray-500 dark:text-gray-400">System</span>
                                    @endif
                                </td>
                                <td class="px-4 py-3">
                                    @if($historyRecord->changed_fields && count($historyRecord->changed_fields) > 0)
                                        <div class="space-y-1">
                                            @foreach($historyRecord->changed_fields as $field)
                                                <div class="text-xs">
                                                    <span class="font-medium text-gray-900 dark:text-gray-100">{{ ucfirst(str_replace('_', ' ', $field)) }}:</span>
                                                    @if(isset($historyRecord->old_values[$field]) && isset($historyRecord->new_values[$field]))
                                                        <div class="ml-2">
                                                            <span class="text-red-600 dark:text-red-400">{{ $historyRecord->old_values[$field] ?? 'null' }}</span>
                                                            <span class="text-gray-500 dark:text-gray-400"> → </span>
                                                            <span class="text-green-600 dark:text-green-400">{{ $historyRecord->new_values[$field] ?? 'null' }}</span>
                                                        </div>
                                                    @elseif(isset($historyRecord->new_values[$field]))
                                                        <span class="text-green-600 dark:text-green-400 ml-2">{{ $historyRecord->new_values[$field] }}</span>
                                                    @endif
                                                </div>
                                            @endforeach
                                        </div>
                                    @elseif($historyRecord->new_values && is_array($historyRecord->new_values))
                                        <div class="text-xs text-gray-600 dark:text-gray-400">
                                            @if($historyRecord->related_model)
                                                Related data updated
                                            @else
                                                {{ count($historyRecord->new_values) }} field(s) changed
                                            @endif
                                        </div>
                                    @else
                                        <span class="text-gray-500 dark:text-gray-400 text-xs">No changes tracked</span>
                                    @endif
                                </td>
                                <td class="px-4 py-3">
                                    @if($historyRecord->related_model)
                                        <div class="text-xs">
                                            <div class="font-medium text-gray-900 dark:text-gray-100">
                                                {{ class_basename($historyRecord->related_model) }}
                                            </div>
                                            @if($historyRecord->related_model_id)
                                                <div class="text-gray-500 dark:text-gray-400">
                                                    ID: {{ $historyRecord->related_model_id }}
                                                </div>
                                            @endif
                                        </div>
                                    @else
                                        <span class="text-gray-500 dark:text-gray-400 text-xs">Direct change</span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        @if($company->history->count() > 20)
            <div class="text-center">
                <p class="text-sm text-gray-500 dark:text-gray-400">
                    Showing latest {{ $company->history->count() }} history records.
                </p>
            </div>
        @endif
    @else
        <div class="text-center py-12">
            <div class="mx-auto h-12 w-12 text-gray-400">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No history available</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                No history records have been created for this company yet.
            </p>
        </div>
    @endif
</div>
