<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Company;
use App\Models\CompanyHistory;
use App\Models\Country;

echo "Testing Pivot Table History\n";
echo "===========================\n\n";

// Get a company to test with
$company = Company::find(3);
if (!$company) {
    echo "No company found with ID 3\n";
    exit;
}

echo "Testing with company: {$company->name} (ID: {$company->company_id})\n";

// Check current history count
$initialHistoryCount = CompanyHistory::count();
echo "Initial history count: $initialHistoryCount\n\n";

// Get some countries to test with
$countries = Country::take(3)->pluck('id')->toArray();
echo "Available countries: " . implode(', ', $countries) . "\n";

// Sync countries using our custom method
echo "Syncing countries...\n";
$company->syncCountries($countries);

// Check if history was created
$newHistoryCount = CompanyHistory::count();
echo "History count after sync: $newHistoryCount\n";

if ($newHistoryCount > $initialHistoryCount) {
    echo "✓ Pivot history record created!\n";
    
    $latestHistory = CompanyHistory::where('change_type', 'countries')->latest()->first();
    if ($latestHistory) {
        echo "Latest countries history:\n";
        echo "- Action: {$latestHistory->action}\n";
        echo "- Change Type: {$latestHistory->change_type}\n";
        echo "- Old Country IDs: " . json_encode($latestHistory->old_country_ids) . "\n";
        echo "- New Country IDs: " . json_encode($latestHistory->new_country_ids) . "\n";
        
        // Test the pivot_changes attribute
        $pivotChanges = $latestHistory->pivot_changes;
        if (isset($pivotChanges['countries'])) {
            $countryChanges = $pivotChanges['countries'];
            echo "- Old Country Names: " . implode(', ', $countryChanges['old']) . "\n";
            echo "- New Country Names: " . implode(', ', $countryChanges['new']) . "\n";
        }
    }
} else {
    echo "✗ No pivot history record was created\n";
}

echo "\nTest completed.\n";
