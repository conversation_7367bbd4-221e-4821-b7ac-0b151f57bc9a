<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Company;
use App\Models\CompanyHistory;
use App\Models\Status;

echo "Testing Company History System\n";
echo "==============================\n\n";

// Check current counts
$companyCount = Company::count();
$historyCount = CompanyHistory::count();

echo "Initial state:\n";
echo "Companies: $companyCount\n";
echo "History records: $historyCount\n\n";

// Create a test company without status_id (nullable)
echo "Creating test company...\n";
$company = Company::create([
    'name' => 'Test Company for History',
    'timezone' => 'UTC',
    'registration_type' => 'invited_by_operator'
]);

echo "Company created with ID: {$company->company_id}\n";

// Check history after creation
$newHistoryCount = CompanyHistory::count();
echo "History records after creation: $newHistoryCount\n";

if ($newHistoryCount > $historyCount) {
    echo "✓ History record created successfully!\n";
    
    $latestHistory = CompanyHistory::latest()->first();
    echo "Latest history record:\n";
    echo "- Action: {$latestHistory->action}\n";
    echo "- Company ID: {$latestHistory->company_id}\n";
    echo "- Company Name: {$latestHistory->name}\n";
    echo "- Created at: {$latestHistory->created_at}\n";
} else {
    echo "✗ No history record was created\n";
}

echo "\nTest completed.\n";
