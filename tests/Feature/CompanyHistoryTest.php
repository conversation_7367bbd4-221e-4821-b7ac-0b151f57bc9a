<?php

namespace Tests\Feature;

use App\Models\Company;
use App\Models\CompanyHistory;
use App\Models\User;
use App\Models\CompanyAddress;
use App\Models\CompanyCountry;
use App\Models\Country;
use App\Models\Status;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CompanyHistoryTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Create necessary statuses and countries for testing
        Status::create(['name' => 'active', 'status_id' => 1]);
        Country::create(['name' => 'United States', 'id' => 1]);
    }

    /** @test */
    public function it_creates_history_record_when_company_is_created()
    {
        $company = Company::factory()->create([
            'name' => 'Test Company',
            'status_id' => 1,
        ]);

        $this->assertDatabaseHas('company_history', [
            'company_id' => $company->company_id,
            'action' => 'created',
            'name' => 'Test Company',
        ]);

        $historyRecord = CompanyHistory::where('company_id', $company->company_id)->first();
        $this->assertEquals('created', $historyRecord->action);
        $this->assertEquals('Test Company', $historyRecord->name);
    }

    /** @test */
    public function it_creates_history_record_when_company_is_updated()
    {
        $company = Company::factory()->create([
            'name' => 'Original Name',
            'status_id' => 1,
        ]);

        // Clear the creation history record for this test
        CompanyHistory::where('company_id', $company->company_id)->delete();

        $company->update(['name' => 'Updated Name']);

        $this->assertDatabaseHas('company_history', [
            'company_id' => $company->company_id,
            'action' => 'updated',
            'name' => 'Updated Name',
        ]);

        $historyRecord = CompanyHistory::where('company_id', $company->company_id)
            ->where('action', 'updated')
            ->first();
        
        $this->assertNotNull($historyRecord);
        $this->assertEquals('Updated Name', $historyRecord->name);
        $this->assertContains('name', $historyRecord->changed_fields);
        $this->assertEquals('Original Name', $historyRecord->old_values['name']);
        $this->assertEquals('Updated Name', $historyRecord->new_values['name']);
    }

    /** @test */
    public function it_creates_history_record_when_company_is_deleted()
    {
        $company = Company::factory()->create([
            'name' => 'Test Company',
            'status_id' => 1,
        ]);

        // Clear the creation history record for this test
        CompanyHistory::where('company_id', $company->company_id)->delete();

        $company->delete();

        $this->assertDatabaseHas('company_history', [
            'company_id' => $company->company_id,
            'action' => 'deleted',
            'name' => 'Test Company',
        ]);
    }

    /** @test */
    public function it_creates_history_record_when_related_user_is_created()
    {
        $company = Company::factory()->create([
            'name' => 'Test Company',
            'status_id' => 1,
        ]);

        // Clear the creation history record for this test
        CompanyHistory::where('company_id', $company->company_id)->delete();

        $user = User::factory()->create([
            'company_id' => $company->company_id,
            'type' => 'customer',
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        $this->assertDatabaseHas('company_history', [
            'company_id' => $company->company_id,
            'action' => 'updated',
            'related_model' => User::class,
            'related_model_id' => $user->user_id,
            'related_action' => 'created',
        ]);
    }

    /** @test */
    public function it_creates_history_record_when_company_address_is_created()
    {
        $company = Company::factory()->create([
            'name' => 'Test Company',
            'status_id' => 1,
        ]);

        // Clear the creation history record for this test
        CompanyHistory::where('company_id', $company->company_id)->delete();

        $address = CompanyAddress::factory()->create([
            'company_id' => $company->company_id,
            'country_id' => 1,
            'address_type' => 'company_address',
        ]);

        $this->assertDatabaseHas('company_history', [
            'company_id' => $company->company_id,
            'action' => 'updated',
            'related_model' => CompanyAddress::class,
            'related_model_id' => $address->company_address_id,
            'related_action' => 'created',
        ]);
    }

    /** @test */
    public function it_creates_history_record_when_company_country_is_added()
    {
        $company = Company::factory()->create([
            'name' => 'Test Company',
            'status_id' => 1,
        ]);

        // Clear the creation history record for this test
        CompanyHistory::where('company_id', $company->company_id)->delete();

        $companyCountry = CompanyCountry::create([
            'company_id' => $company->company_id,
            'country_id' => 1,
        ]);

        $this->assertDatabaseHas('company_history', [
            'company_id' => $company->company_id,
            'action' => 'updated',
            'related_model' => CompanyCountry::class,
            'related_model_id' => $companyCountry->company_country_id,
            'related_action' => 'created',
        ]);
    }

    /** @test */
    public function company_history_relationship_works()
    {
        $company = Company::factory()->create([
            'name' => 'Test Company',
            'status_id' => 1,
        ]);

        $historyRecords = $company->history;
        
        $this->assertGreaterThan(0, $historyRecords->count());
        $this->assertEquals($company->company_id, $historyRecords->first()->company_id);
    }

    /** @test */
    public function history_record_has_proper_relationships()
    {
        $company = Company::factory()->create([
            'name' => 'Test Company',
            'status_id' => 1,
        ]);

        $historyRecord = CompanyHistory::where('company_id', $company->company_id)->first();
        
        $this->assertNotNull($historyRecord->company);
        $this->assertEquals($company->company_id, $historyRecord->company->company_id);
    }
}
