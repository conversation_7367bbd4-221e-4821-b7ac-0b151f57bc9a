<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Company;
use App\Models\CompanyHistory;

echo "Testing Updated Company History System\n";
echo "=====================================\n\n";

// Check current counts
$companyCount = Company::count();
$historyCount = CompanyHistory::count();

echo "Initial state:\n";
echo "Companies: $companyCount\n";
echo "History records: $historyCount\n\n";

// Create a test company
echo "Creating test company...\n";
$company = Company::create([
    'name' => 'Updated Test Company',
    'website' => 'https://example.com',
    'timezone' => 'UTC',
    'registration_type' => 'invited_by_operator'
]);

echo "Company created with ID: {$company->company_id}\n";

// Check history after creation
$newHistoryCount = CompanyHistory::count();
echo "History records after creation: $newHistoryCount\n";

if ($newHistoryCount > $historyCount) {
    echo "✓ History record created successfully!\n";
    
    $latestHistory = CompanyHistory::latest()->first();
    echo "Latest history record:\n";
    echo "- Action: {$latestHistory->action}\n";
    echo "- Change Type: {$latestHistory->change_type}\n";
    echo "- Company Name: {$latestHistory->name}\n";
    echo "- Website: {$latestHistory->website}\n";
    echo "- Created at: {$latestHistory->created_at}\n\n";
} else {
    echo "✗ No history record was created\n";
}

// Test updating the company
echo "Updating company...\n";
$company->update([
    'name' => 'Updated Company Name',
    'number_of_employees' => 50
]);

$finalHistoryCount = CompanyHistory::count();
echo "History records after update: $finalHistoryCount\n";

if ($finalHistoryCount > $newHistoryCount) {
    echo "✓ Update history record created successfully!\n";

    $allHistory = CompanyHistory::orderBy('created_at', 'desc')->take(2)->get();
    foreach ($allHistory as $index => $history) {
        echo "History record " . ($index + 1) . ":\n";
        echo "- Action: {$history->action}\n";
        echo "- Change Type: {$history->change_type}\n";
        echo "- Changed Fields: " . implode(', ', $history->changed_fields ?? []) . "\n";
        echo "- Old Values: " . json_encode($history->old_values) . "\n";
        echo "- New Values: " . json_encode($history->new_values) . "\n\n";
    }
} else {
    echo "✗ No update history record was created\n";
}

echo "\nTest completed.\n";
