<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('company_history', function (Blueprint $table) {
            $table->id('company_history_id');
            
            // Reference to the original company
            $table->foreignId('company_id')->constrained('companies', 'company_id')->onDelete('cascade');
            
            // Action tracking
            $table->enum('action', ['created', 'updated', 'deleted', 'restored'])->index();
            $table->string('related_model')->nullable()->index(); // For tracking related model changes
            $table->unsignedBigInteger('related_model_id')->nullable()->index(); // ID of related model
            $table->string('related_action')->nullable(); // Action on related model
            
            // User who performed the action
            $table->foreignId('performed_by')->nullable()->constrained('users', 'user_id')->nullOnDelete();
            
            // Company data snapshot (all fields from companies table)
            $table->string('public_company_id')->default('');
            $table->string('name')->nullable();
            $table->foreignId('status_id')->nullable();
            $table->string('timezone')->nullable();
            $table->string('website')->nullable();
            $table->integer('number_of_employees')->nullable();
            $table->enum('registration_type', ['self_registered', 'invited_by_operator'])->default('invited_by_operator');
            $table->timestamp('invitation_expires_at')->nullable();
            $table->timestamp('last_activity_at')->nullable();
            
            // Original timestamps from company
            $table->timestamp('original_created_at')->nullable();
            $table->timestamp('original_updated_at')->nullable();
            $table->timestamp('original_deleted_at')->nullable();
            $table->foreignId('original_created_by')->nullable();
            $table->foreignId('original_updated_by')->nullable();
            $table->foreignId('original_deleted_by')->nullable();
            
            // Changed fields tracking
            $table->json('changed_fields')->nullable(); // Array of field names that changed
            $table->json('old_values')->nullable(); // Previous values
            $table->json('new_values')->nullable(); // New values
            
            // Additional context
            $table->text('notes')->nullable(); // Optional notes about the change
            $table->json('request_data')->nullable(); // Store request context if needed
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            
            // History record timestamps
            $table->timestamps();
            
            // Indexes for better performance
            $table->index(['company_id', 'action']);
            $table->index(['company_id', 'created_at']);
            $table->index(['performed_by', 'created_at']);
            $table->index(['related_model', 'related_model_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('company_history');
    }
};
