<?php

namespace Database\Factories;

use App\Models\CompanyAddress;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CompanyAddress>
 */
class CompanyAddressFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CompanyAddress::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'company_id' => 1,
            'country_id' => 1,
            'tax_id' => $this->faker->numerify('TAX-#########'),
            'commercial_registration_number' => $this->faker->numerify('REG-#########'),
            'state' => $this->faker->state(),
            'city' => $this->faker->city(),
            'zip' => $this->faker->postcode(),
            'address_1' => $this->faker->streetAddress(),
            'address_type' => $this->faker->randomElement(['company_address', 'billing_address']),
        ];
    }
}
