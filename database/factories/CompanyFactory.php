<?php

namespace Database\Factories;

use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Company>
 */
class CompanyFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Company::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company(),
            'status_id' => 1, // Assuming active status
            'timezone' => $this->faker->timezone(),
            'website' => $this->faker->url(),
            'number_of_employees' => $this->faker->numberBetween(1, 1000),
            'registration_type' => $this->faker->randomElement(['self_registered', 'invited_by_operator']),
        ];
    }
}
