<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Company;
use App\Models\CompanyHistory;

echo "Testing Type Conversion Fix\n";
echo "===========================\n\n";

// Get a company to test with
$company = Company::find(3);
if (!$company) {
    echo "No company found with ID 3\n";
    exit;
}

echo "Testing with company: {$company->name} (ID: {$company->company_id})\n";

// Check current history count
$initialHistoryCount = CompanyHistory::count();
echo "Initial history count: $initialHistoryCount\n\n";

// Test 1: Sync with same sectors (should NOT create history)
echo "Test 1: Syncing with same sectors...\n";
$currentSectorIds = $company->sectors()->pluck('sectors.sector_id')->toArray();
echo "Current sector IDs: " . json_encode($currentSectorIds) . "\n";

$company->syncSectors($currentSectorIds);

$afterSameSync = CompanyHistory::count();
echo "History count after same sync: $afterSameSync\n";

if ($afterSameSync === $initialHistoryCount) {
    echo "✓ No history created for identical sync (correct behavior)\n\n";
} else {
    echo "✗ History was created for identical sync (incorrect behavior)\n\n";
}

// Test 2: Sync with different sectors (should create history)
echo "Test 2: Syncing with different sectors...\n";
$newSectorIds = [1, 2]; // Use different sector IDs
echo "New sector IDs: " . json_encode($newSectorIds) . "\n";

$company->syncSectors($newSectorIds);

$afterDifferentSync = CompanyHistory::count();
echo "History count after different sync: $afterDifferentSync\n";

if ($afterDifferentSync > $afterSameSync) {
    echo "✓ History created for different sync (correct behavior)\n";
    
    $latestHistory = CompanyHistory::where('change_type', 'sectors')->latest()->first();
    if ($latestHistory) {
        echo "Latest sectors history:\n";
        echo "- Old Sector IDs: " . json_encode($latestHistory->old_sector_ids) . "\n";
        echo "- New Sector IDs: " . json_encode($latestHistory->new_sector_ids) . "\n";
    }
} else {
    echo "✗ No history created for different sync (incorrect behavior)\n";
}

echo "\nTest completed.\n";
